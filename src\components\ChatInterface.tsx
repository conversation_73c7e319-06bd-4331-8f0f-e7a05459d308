import React, { useEffect, useState } from 'react';
import MessageBubble from './MessageBubble';
import InputArea from './InputArea';
import LoadingIndicator from './LoadingIndicator';
import { useChatStore } from '../stores/chatStore';
import { useLLM, useLLMHealthCheck } from '../hooks/useLLM';
import { AlertCircle, Wifi, WifiOff } from 'lucide-react';

const ChatInterface: React.FC = () => {
  const { messages, addMessage, setLoading, setError } = useChatStore();
  const { queryLLM, isLoading, error, clearError } = useLLM();
  const { isAvailable, checkHealth } = useLLMHealthCheck();
  const [showHealthWarning, setShowHealthWarning] = useState(false);

  useEffect(() => {
    const scrollToBottom = () => {
      const chatWindow = document.getElementById('chat-window');
      if (chatWindow) {
        chatWindow.scrollTop = chatWindow.scrollHeight;
      }
    };

    scrollToBottom();
  }, [messages]);

  // Check LLM health on component mount
  useEffect(() => {
    const performHealthCheck = async () => {
      const healthy = await checkHealth();
      setShowHealthWarning(!healthy);
    };

    performHealthCheck();
  }, [checkHealth]);

  // Sync loading state with chat store
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  // Sync error state with chat store
  useEffect(() => {
    if (error) {
      setError(error.message);
    } else {
      setError(null);
    }
  }, [error, setError]);

  const handleSendMessage = async (content: string) => {
    // Clear any previous errors
    clearError();
    setError(null);

    // Add user message
    addMessage(content, 'user');

    try {
      // Generate AI response
      const response = await queryLLM(content);

      // Add AI response
      addMessage(response, 'assistant');
    } catch (error) {
      console.error('Failed to get LLM response:', error);

      // Add error message to chat
      const errorMessage = error instanceof Error
        ? error.message
        : 'Sorry, I encountered an error while processing your request. Please try again.';

      addMessage(`Error: ${errorMessage}`, 'assistant');
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Health warning banner */}
      {showHealthWarning && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 px-4 py-3">
          <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
            <WifiOff className="h-4 w-4" />
            <span className="text-sm">
              AI service is not available. Please ensure Ollama is running with the gemma2:2b model.
            </span>
            <button
              type="button"
              onClick={async () => {
                const healthy = await checkHealth();
                setShowHealthWarning(!healthy);
              }}
              className="ml-auto text-xs underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Error banner */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 px-4 py-3">
          <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error.message}</span>
            <button
              type="button"
              onClick={clearError}
              className="ml-auto text-xs underline hover:no-underline"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Chat window */}
      <div
        id="chat-window"
        className="flex-1 overflow-y-auto px-4 py-6 bg-gray-100 dark:bg-gray-900"
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                {isAvailable ? (
                  <Wifi className="h-8 w-8 text-green-500" />
                ) : (
                  <WifiOff className="h-8 w-8 text-red-500" />
                )}
              </div>
              <p className="text-lg font-medium mb-2">
                {isAvailable ? 'AI Assistant Ready' : 'AI Service Unavailable'}
              </p>
              <p className="text-sm">
                {isAvailable
                  ? 'Start a conversation by typing a message below.'
                  : 'Please ensure Ollama is running with the gemma2:2b model.'
                }
              </p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))
        )}

        {/* Loading indicator */}
        {isLoading && <LoadingIndicator />}
      </div>

      {/* Input area */}
      <InputArea onSendMessage={handleSendMessage} disabled={isLoading || !isAvailable} />
    </div>
  );
};

export default ChatInterface;

